/**
 * 应用配置模块
 * 统一管理应用配置常量，采用业内最佳实践
 * 不依赖环境变量，直接在代码中声明配置常量
 */

/**
 * 环境类型枚举
 */
const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
};

/**
 * 获取当前环境
 * 优先级：命令行参数 > NODE_ENV > 默认值
 */
function getCurrentEnvironment() {
  // 检查命令行参数
  if (typeof process !== 'undefined' && process.argv) {
    if (process.argv.includes('--dev') || process.argv.includes('--development')) {
      return ENVIRONMENTS.DEVELOPMENT;
    }
    if (process.argv.includes('--prod') || process.argv.includes('--production')) {
      return ENVIRONMENTS.PRODUCTION;
    }
  }

  // 检查 NODE_ENV（仅在 Node.js 环境中可用）
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) {
    return process.env.NODE_ENV;
  }

  // 默认为生产环境
  return ENVIRONMENTS.PRODUCTION;
}

/**
 * 应用配置常量
 * 所有配置直接在此声明，不依赖外部环境变量
 */
const APP_CONFIG = {
  // 基础配置
  NODE_ENV: getCurrentEnvironment(),

  // API 配置 - 按环境分组
  API: {
    [ENVIRONMENTS.DEVELOPMENT]: {
      BASE_URL: 'http://localhost:3000',
      TIMEOUT: 600000, // 10分钟，适应大文件上传
      RETRY_TIMES: 3
    },

    [ENVIRONMENTS.PRODUCTION]: {
      BASE_URL: 'https://aitools.vvvlin.com',
      TIMEOUT: 600000, // 10分钟，适应大文件上传
      RETRY_TIMES: 3
    }
  },

  // 认证配置
  AUTH: {
    DEFAULT_ADMIN_CODE: 'ADMIN2024',
    DEFAULT_USER_CODE: 'USER2024'
  },

  // 界面配置
  UI: {
    DEFAULT_LANGUAGE: 'zh-CN',
    DEFAULT_THEME: 'auto',
    DEV_TOOLS_ENABLED: true
  },

  // 窗口配置
  WINDOWS: {
    MAIN: { WIDTH: 1200, HEIGHT: 800 },
    LOGIN: { WIDTH: 1200, HEIGHT: 800 },
    SETTINGS: { WIDTH: 1200, HEIGHT: 800 },
    ADMIN: { WIDTH: 1200, HEIGHT: 800 }
  },

  // 开发调试配置
  DEBUG: {
    ENABLED: true,
    LOG_LEVEL: 'debug'
  }
};

/**
 * 环境检查函数
 */
function isDevelopment() {
  return getCurrentEnvironment() === ENVIRONMENTS.DEVELOPMENT;
}

function isProduction() {
  return getCurrentEnvironment() === ENVIRONMENTS.PRODUCTION;
}



/**
 * 获取当前环境的 API 配置
 * @returns {object} API 配置对象
 */
function getApiConfig() {
  const env = getCurrentEnvironment();
  return APP_CONFIG.API[env] || APP_CONFIG.API[ENVIRONMENTS.DEVELOPMENT];
}

/**
 * 获取 API 基础 URL（根据当前环境）
 * @returns {string} API 基础 URL
 */
function getApiBaseUrl() {
  return getApiConfig().BASE_URL;
}

/**
 * 获取 API 超时时间
 * @returns {number} 超时时间（毫秒）
 */
function getApiTimeout() {
  return getApiConfig().TIMEOUT;
}

/**
 * 获取 API 重试次数
 * @returns {number} 重试次数
 */
function getApiRetryTimes() {
  return getApiConfig().RETRY_TIMES;
}

/**
 * 获取窗口配置
 * @param {string} windowType - 窗口类型 ('main' | 'login' | 'settings' | 'admin')
 * @returns {object} 窗口配置
 */
function getWindowConfig(windowType) {
  const windowKey = windowType ? windowType.toUpperCase() : 'MAIN';
  const config = APP_CONFIG.WINDOWS[windowKey] || APP_CONFIG.WINDOWS.MAIN;

  return {
    width: config.WIDTH,
    height: config.HEIGHT
  };
}

/**
 * 打印环境配置信息（仅开发环境）
 */
function printEnvConfig() {
  if (isDevelopment()) {
    try {
      const packageJson = require('../../../package.json');
      console.log('\n📋 应用配置信息:');
      console.log(`   环境: ${getCurrentEnvironment()}`);
      console.log(`   应用名称: ${packageJson.build?.productName || packageJson.name}`);
      console.log(`   应用版本: ${packageJson.version}`);
      console.log(`   API 地址: ${getApiBaseUrl()}`);
      console.log(`   默认语言: ${APP_CONFIG.UI.DEFAULT_LANGUAGE}`);
      console.log(`   默认主题: ${APP_CONFIG.UI.DEFAULT_THEME}`);
      console.log(`   调试模式: ${APP_CONFIG.DEBUG.ENABLED}`);
      console.log('');
    } catch (error) {
      console.log('\n📋 应用配置信息:');
      console.log(`   环境: ${getCurrentEnvironment()}`);
      console.log(`   API 地址: ${getApiBaseUrl()}`);
      console.log(`   默认语言: ${APP_CONFIG.UI.DEFAULT_LANGUAGE}`);
      console.log(`   默认主题: ${APP_CONFIG.UI.DEFAULT_THEME}`);
      console.log(`   调试模式: ${APP_CONFIG.DEBUG.ENABLED}`);
      console.log('');
    }
  }
}

// 导出所有配置和函数
module.exports = {
  // 常量
  ENVIRONMENTS,
  APP_CONFIG,

  // 核心函数
  getCurrentEnvironment,
  isDevelopment,
  isProduction,


  // API 相关
  getApiConfig,
  getApiBaseUrl,
  getApiTimeout,
  getApiRetryTimes,

  // 窗口配置
  getWindowConfig,

  // 工具函数
  printEnvConfig
};
