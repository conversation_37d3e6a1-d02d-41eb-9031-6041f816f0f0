/**
 * electron-updater 高级配置
 * 专门处理更新下载的超时、重试等问题，支持断点续传
 */

const { autoUpdater } = require('electron-updater');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 下载状态跟踪
let downloadState = {
  isDownloading: false,
  downloadedBytes: 0,
  totalBytes: 0,
  downloadUrl: null,
  tempFilePath: null,
  lastProgressTime: Date.now()
};

/**
 * 配置 electron-updater 的高级选项
 */
function configureUpdaterAdvanced() {
  // 设置更长的超时时间
  if (autoUpdater.httpExecutor) {
    // 设置HTTP请求超时为10分钟
    autoUpdater.httpExecutor.maxRedirects = 10;

    // 如果有requestOptions，设置超时
    if (autoUpdater.httpExecutor.requestOptions) {
      autoUpdater.httpExecutor.requestOptions.timeout = 600000; // 10分钟
    }
  }

  // 设置请求头，避免缓存问题
  autoUpdater.requestHeaders = {
    'Cache-Control': 'no-cache',
    'User-Agent': 'AiTools-Updater/1.0'
  };

  // 禁用自动安装，让用户控制
  autoUpdater.autoInstallOnAppQuit = false;

  console.log('electron-updater 高级配置已应用');
}

/**
 * 获取更新文件的下载URL
 * @returns {Promise<string>} 下载URL
 */
async function getUpdateDownloadUrl() {
  try {
    // 首先检查更新
    const updateInfo = await autoUpdater.checkForUpdates();
    if (!updateInfo || !updateInfo.updateInfo) {
      throw new Error('没有可用的更新');
    }

    const updateData = updateInfo.updateInfo;
    console.log('更新信息:', updateData);

    // 根据平台获取下载URL
    let downloadUrl;
    if (process.platform === 'win32') {
      // Windows平台
      const file = updateData.files.find(f => f.url.endsWith('.exe') || f.url.endsWith('.msi'));
      if (file) {
        downloadUrl = file.url;
      }
    } else if (process.platform === 'darwin') {
      // macOS平台
      const file = updateData.files.find(f => f.url.endsWith('.dmg') || f.url.endsWith('.zip'));
      if (file) {
        downloadUrl = file.url;
      }
    } else {
      // Linux平台
      const file = updateData.files.find(f => f.url.endsWith('.AppImage') || f.url.endsWith('.deb'));
      if (file) {
        downloadUrl = file.url;
      }
    }

    if (!downloadUrl) {
      throw new Error('找不到适合当前平台的更新文件');
    }

    // 如果是相对URL，需要拼接基础URL
    if (!downloadUrl.startsWith('http')) {
      const baseUrl = updateData.releaseNotes || 'https://github.com/laixiao/AiTools/releases/download/';
      downloadUrl = baseUrl + downloadUrl;
    }

    console.log('获取到下载URL:', downloadUrl);
    return downloadUrl;
  } catch (error) {
    console.error('获取下载URL失败:', error);
    throw error;
  }
}

/**
 * 支持断点续传的下载函数
 * @param {string} url - 下载URL
 * @param {string} filePath - 保存文件路径
 * @param {Function} onProgress - 进度回调
 * @returns {Promise} 下载Promise
 */
function downloadWithResume(url, filePath, onProgress) {
  return new Promise((resolve, reject) => {
    let startByte = 0;
    let lastProgressTime = Date.now();
    let speedCalculator = {
      lastBytes: 0,
      lastTime: Date.now(),
      samples: []
    };

    // 检查是否存在部分下载的文件
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      startByte = stats.size;
      speedCalculator.lastBytes = startByte;
      console.log(`发现部分下载文件，从字节 ${startByte} 继续下载`);
    }

    const protocol = url.startsWith('https:') ? https : http;
    const options = {
      headers: {
        'Range': `bytes=${startByte}-`,
        'User-Agent': 'AiTools-Updater/1.0',
        'Accept': '*/*',
        'Connection': 'keep-alive'
      }
    };

    console.log(`开始下载: ${url}`);
    console.log(`Range: bytes=${startByte}-`);

    const request = protocol.get(url, options, (response) => {
      const statusCode = response.statusCode;
      console.log(`HTTP状态码: ${statusCode}`);
      console.log(`响应头:`, response.headers);

      if (statusCode === 206 || (statusCode === 200 && startByte === 0)) {
        // 206: 部分内容（续传）, 200: 完整内容（新下载）
        const contentLength = parseInt(response.headers['content-length']) || 0;
        const totalSize = statusCode === 206 ? contentLength + startByte : contentLength;

        console.log(`文件总大小: ${totalSize} 字节`);
        console.log(`已下载: ${startByte} 字节`);
        console.log(`剩余: ${contentLength} 字节`);

        const writeStream = fs.createWriteStream(filePath, { flags: startByte > 0 ? 'a' : 'w' });
        let downloadedBytes = startByte;

        response.on('data', (chunk) => {
          downloadedBytes += chunk.length;
          writeStream.write(chunk);

          const now = Date.now();

          // 计算下载速度
          if (now - speedCalculator.lastTime >= 1000) { // 每秒计算一次
            const bytesInSecond = downloadedBytes - speedCalculator.lastBytes;
            const timeInSecond = (now - speedCalculator.lastTime) / 1000;
            const speed = bytesInSecond / timeInSecond;

            speedCalculator.samples.push(speed);
            if (speedCalculator.samples.length > 5) {
              speedCalculator.samples.shift(); // 保持最近5个样本
            }

            speedCalculator.lastBytes = downloadedBytes;
            speedCalculator.lastTime = now;
          }

          // 计算平均速度
          const avgSpeed = speedCalculator.samples.length > 0
            ? speedCalculator.samples.reduce((a, b) => a + b, 0) / speedCalculator.samples.length
            : 0;

          if (onProgress && now - lastProgressTime >= 500) { // 每500ms更新一次进度
            onProgress({
              transferred: downloadedBytes,
              total: totalSize,
              percent: totalSize > 0 ? (downloadedBytes / totalSize) * 100 : 0,
              bytesPerSecond: Math.round(avgSpeed),
              delta: chunk.length
            });
            lastProgressTime = now;
          }
        });

        response.on('end', () => {
          writeStream.end();
          console.log('下载完成，总大小:', downloadedBytes);
          resolve({ path: filePath, size: downloadedBytes });
        });

        response.on('error', (error) => {
          console.error('响应错误:', error);
          writeStream.destroy();
          reject(error);
        });

        writeStream.on('error', (error) => {
          console.error('写入文件错误:', error);
          reject(error);
        });

      } else if (statusCode === 416) {
        // 416: 请求范围不满足，文件可能已经完整
        console.log('文件可能已经下载完成');
        resolve({ path: filePath, size: startByte });
      } else if (statusCode === 302 || statusCode === 301) {
        // 重定向
        const redirectUrl = response.headers.location;
        console.log('重定向到:', redirectUrl);
        response.destroy();
        downloadWithResume(redirectUrl, filePath, onProgress).then(resolve).catch(reject);
      } else {
        reject(new Error(`HTTP ${statusCode}: ${response.statusMessage}`));
      }
    });

    request.on('error', (error) => {
      console.error('请求错误:', error);
      reject(error);
    });

    // 设置超时
    request.setTimeout(300000, () => { // 5分钟超时
      console.error('请求超时');
      request.destroy();
      reject(new Error('下载超时'));
    });
  });
}

/**
 * 创建带有超时控制的下载Promise
 * @param {number} timeoutMs - 超时时间（毫秒）
 * @returns {Promise} 下载Promise
 */
function createDownloadWithTimeout(timeoutMs = 600000) { // 默认10分钟
  return new Promise((resolve, reject) => {
    let isResolved = false;
    let progressTimeout;

    // 设置总超时
    const totalTimeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        reject(new Error(`下载总超时 (${timeoutMs / 1000}秒)`));
      }
    }, timeoutMs);

    // 设置进度超时（如果60秒内没有进度更新，认为连接断开）
    const resetProgressTimeout = () => {
      if (progressTimeout) clearTimeout(progressTimeout);
      progressTimeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          clearTimeout(totalTimeoutId);
          reject(new Error('下载进度超时，连接可能已断开'));
        }
      }, 60000); // 60秒无进度则超时
    };

    resetProgressTimeout();

    // 监听下载进度
    const onProgress = (progress) => {
      resetProgressTimeout(); // 有进度就重置超时
      downloadState.downloadedBytes = progress.transferred;
      downloadState.totalBytes = progress.total;
      downloadState.lastProgressTime = Date.now();
    };

    // 监听下载完成
    const onDownloaded = (info) => {
      if (!isResolved) {
        isResolved = true;
        clearTimeout(totalTimeoutId);
        if (progressTimeout) clearTimeout(progressTimeout);
        autoUpdater.removeListener('update-downloaded', onDownloaded);
        autoUpdater.removeListener('error', onError);
        autoUpdater.removeListener('download-progress', onProgress);
        resolve(info);
      }
    };

    // 监听错误
    const onError = (error) => {
      if (!isResolved) {
        isResolved = true;
        clearTimeout(totalTimeoutId);
        if (progressTimeout) clearTimeout(progressTimeout);
        autoUpdater.removeListener('update-downloaded', onDownloaded);
        autoUpdater.removeListener('error', onError);
        autoUpdater.removeListener('download-progress', onProgress);
        reject(error);
      }
    };

    autoUpdater.on('update-downloaded', onDownloaded);
    autoUpdater.on('error', onError);
    autoUpdater.on('download-progress', onProgress);

    // 开始下载
    autoUpdater.downloadUpdate().catch(onError);
  });
}

/**
 * 使用自定义下载替代electron-updater
 * @param {number} maxRetries - 最大重试次数
 * @param {Function} onProgress - 进度回调
 * @returns {Promise} 下载结果
 */
async function downloadUpdateWithResume(maxRetries = 5, onProgress) {
  let lastError;
  let downloadUrl;
  let tempFilePath;

  try {
    // 获取下载URL
    downloadUrl = await getUpdateDownloadUrl();

    // 创建临时文件路径
    const os = require('os');
    const crypto = require('crypto');
    const urlHash = crypto.createHash('md5').update(downloadUrl).digest('hex');
    tempFilePath = path.join(os.tmpdir(), `aitools-update-${urlHash}.tmp`);

    console.log('下载URL:', downloadUrl);
    console.log('临时文件路径:', tempFilePath);

    downloadState.downloadUrl = downloadUrl;
    downloadState.tempFilePath = tempFilePath;

  } catch (error) {
    console.error('获取下载信息失败:', error);
    throw error;
  }

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`开始第 ${attempt} 次下载尝试...`);

      downloadState.isDownloading = true;
      downloadState.lastProgressTime = Date.now();

      const result = await downloadWithResume(downloadUrl, tempFilePath, (progress) => {
        downloadState.downloadedBytes = progress.transferred;
        downloadState.totalBytes = progress.total;
        downloadState.lastProgressTime = Date.now();

        if (onProgress) {
          onProgress(progress);
        }
      });

      console.log(`第 ${attempt} 次下载尝试成功，文件大小: ${result.size} 字节`);

      downloadState.isDownloading = false;
      return result;

    } catch (error) {
      lastError = error;
      downloadState.isDownloading = false;

      console.error(`第 ${attempt} 次下载尝试失败:`, error.message);

      // 分析错误类型，决定重试策略
      const isNetworkError = isRetryableError(error);

      if (attempt < maxRetries && isNetworkError) {
        // 使用指数退避
        const delay = Math.min(3000 * Math.pow(2, attempt - 1), 30000);
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else if (attempt >= maxRetries) {
        console.log('已达到最大重试次数');
        break;
      } else {
        console.log('非网络错误，停止重试');
        break;
      }
    }
  }

  throw lastError;
}

/**
 * 带重试的下载函数，支持智能重试和进度监控
 * @param {number} maxRetries - 最大重试次数
 * @param {number} timeoutMs - 每次尝试的超时时间
 * @returns {Promise} 下载结果
 */
async function downloadWithRetry(maxRetries = 5, timeoutMs = 600000) {
  // 首先尝试使用自定义下载（支持断点续传）
  try {
    console.log('使用自定义下载方式（支持断点续传）...');
    return await downloadUpdateWithResume(maxRetries, (progress) => {
      // 发送进度事件，模拟electron-updater的行为
      if (global.mainWindow) {
        global.mainWindow.webContents.send('update-download-progress', progress);
      }
    });
  } catch (error) {
    console.error('自定义下载失败，回退到electron-updater:', error.message);

    // 回退到原有的electron-updater方式
    let lastError;
    let consecutiveFailures = 0;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`回退方式第 ${attempt} 次下载尝试...`);

        // 每次重试前重新配置
        configureUpdaterAdvanced();

        // 重置下载状态
        downloadState.isDownloading = true;
        downloadState.downloadedBytes = 0;
        downloadState.totalBytes = 0;
        downloadState.lastProgressTime = Date.now();

        const result = await createDownloadWithTimeout(timeoutMs);
        console.log(`回退方式第 ${attempt} 次下载尝试成功`);

        downloadState.isDownloading = false;
        consecutiveFailures = 0;
        return result;

      } catch (error) {
        lastError = error;
        consecutiveFailures++;
        downloadState.isDownloading = false;

        console.error(`回退方式第 ${attempt} 次下载尝试失败:`, error.message);

        // 分析错误类型，决定重试策略
        const isNetworkError = isRetryableError(error);
        const isProgressTimeout = error.message.includes('进度超时');

        if (attempt < maxRetries && (isNetworkError || isProgressTimeout)) {
          // 根据错误类型调整延迟时间
          let delay;
          if (isProgressTimeout) {
            delay = Math.min(2000 * attempt, 10000);
          } else {
            delay = Math.min(3000 * Math.pow(2, attempt - 1), 30000);
          }

          console.log(`检测到${isProgressTimeout ? '进度超时' : '网络错误'}，等待 ${delay}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, delay));

          if (consecutiveFailures >= 3) {
            console.log('连续失败次数较多，增加额外延迟...');
            await new Promise(resolve => setTimeout(resolve, 5000));
          }
        } else if (attempt >= maxRetries) {
          console.log('已达到最大重试次数');
          break;
        } else {
          console.log('非网络错误，停止重试');
          break;
        }
      }
    }

    throw lastError;
  }
}

/**
 * 检查错误是否可重试
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否可重试
 */
function isRetryableError(error) {
  if (!error) return false;
  
  const message = error.message || String(error);
  const retryablePatterns = [
    'timeout',
    'TIMEOUT',
    'ECONNRESET',
    'ENOTFOUND',
    'ETIMEDOUT',
    'socket hang up',
    'ERR_CONNECTION_RESET',
    'ERR_CONNECTION_CLOSED',
    'Request timeout',
    'Download timeout',
    'ERR_SOCKET_TIMEOUT',
    'ERR_STREAM_PREMATURE_CLOSE'
  ];
  
  return retryablePatterns.some(pattern => 
    message.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * 获取当前下载状态
 * @returns {object} 下载状态信息
 */
function getDownloadState() {
  return {
    ...downloadState,
    isStalled: downloadState.isDownloading &&
               (Date.now() - downloadState.lastProgressTime) > 30000 // 30秒无进度认为卡住
  };
}

/**
 * 重置下载状态
 */
function resetDownloadState() {
  downloadState.isDownloading = false;
  downloadState.downloadedBytes = 0;
  downloadState.totalBytes = 0;
  downloadState.downloadUrl = null;
  downloadState.tempFilePath = null;
  downloadState.lastProgressTime = Date.now();
}

/**
 * 强制清理下载资源
 */
function cleanupDownload() {
  try {
    if (downloadState.tempFilePath && fs.existsSync(downloadState.tempFilePath)) {
      fs.unlinkSync(downloadState.tempFilePath);
      console.log('已清理临时下载文件');
    }
  } catch (error) {
    console.warn('清理下载文件失败:', error.message);
  }
  resetDownloadState();
}

module.exports = {
  configureUpdaterAdvanced,
  createDownloadWithTimeout,
  downloadWithRetry,
  downloadWithResume,
  isRetryableError,
  getDownloadState,
  resetDownloadState,
  cleanupDownload
};
