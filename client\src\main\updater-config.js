/**
 * electron-updater 高级配置
 * 专门处理更新下载的超时、重试等问题
 */

const { autoUpdater } = require('electron-updater');

/**
 * 配置 electron-updater 的高级选项
 */
function configureUpdaterAdvanced() {
  // 设置更长的超时时间
  if (autoUpdater.httpExecutor) {
    // 设置HTTP请求超时为10分钟
    autoUpdater.httpExecutor.maxRedirects = 10;
    
    // 如果有requestOptions，设置超时
    if (autoUpdater.httpExecutor.requestOptions) {
      autoUpdater.httpExecutor.requestOptions.timeout = 600000; // 10分钟
    }
  }

  // 设置请求头，避免缓存问题
  autoUpdater.requestHeaders = {
    'Cache-Control': 'no-cache',
    'User-Agent': 'AiTools-Updater/1.0'
  };

  // 禁用自动安装，让用户控制
  autoUpdater.autoInstallOnAppQuit = false;

  console.log('electron-updater 高级配置已应用');
}

/**
 * 创建带有超时控制的下载Promise
 * @param {number} timeoutMs - 超时时间（毫秒）
 * @returns {Promise} 下载Promise
 */
function createDownloadWithTimeout(timeoutMs = 600000) { // 默认10分钟
  return new Promise((resolve, reject) => {
    let isResolved = false;
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        reject(new Error(`下载超时 (${timeoutMs / 1000}秒)`));
      }
    }, timeoutMs);

    // 监听下载完成
    const onDownloaded = (info) => {
      if (!isResolved) {
        isResolved = true;
        clearTimeout(timeoutId);
        autoUpdater.removeListener('update-downloaded', onDownloaded);
        autoUpdater.removeListener('error', onError);
        resolve(info);
      }
    };

    // 监听错误
    const onError = (error) => {
      if (!isResolved) {
        isResolved = true;
        clearTimeout(timeoutId);
        autoUpdater.removeListener('update-downloaded', onDownloaded);
        autoUpdater.removeListener('error', onError);
        reject(error);
      }
    };

    autoUpdater.on('update-downloaded', onDownloaded);
    autoUpdater.on('error', onError);

    // 开始下载
    autoUpdater.downloadUpdate().catch(onError);
  });
}

/**
 * 带重试的下载函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} timeoutMs - 每次尝试的超时时间
 * @returns {Promise} 下载结果
 */
async function downloadWithRetry(maxRetries = 5, timeoutMs = 600000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`开始第 ${attempt} 次下载尝试...`);
      
      // 每次重试前重新配置
      configureUpdaterAdvanced();
      
      const result = await createDownloadWithTimeout(timeoutMs);
      console.log(`第 ${attempt} 次下载尝试成功`);
      return result;
      
    } catch (error) {
      lastError = error;
      console.error(`第 ${attempt} 次下载尝试失败:`, error.message);
      
      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        const delay = Math.min(3000 * Math.pow(2, attempt - 1), 30000); // 指数退避，最大30秒
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

/**
 * 检查错误是否可重试
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否可重试
 */
function isRetryableError(error) {
  if (!error) return false;
  
  const message = error.message || String(error);
  const retryablePatterns = [
    'timeout',
    'TIMEOUT',
    'ECONNRESET',
    'ENOTFOUND',
    'ETIMEDOUT',
    'socket hang up',
    'ERR_CONNECTION_RESET',
    'ERR_CONNECTION_CLOSED',
    'Request timeout',
    'Download timeout',
    'ERR_SOCKET_TIMEOUT',
    'ERR_STREAM_PREMATURE_CLOSE'
  ];
  
  return retryablePatterns.some(pattern => 
    message.toLowerCase().includes(pattern.toLowerCase())
  );
}

module.exports = {
  configureUpdaterAdvanced,
  createDownloadWithTimeout,
  downloadWithRetry,
  isRetryableError
};
