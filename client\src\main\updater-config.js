/**
 * electron-updater 高级配置
 * 专门处理更新下载的超时、重试等问题，支持断点续传
 */

const { autoUpdater } = require('electron-updater');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 下载状态跟踪
let downloadState = {
  isDownloading: false,
  downloadedBytes: 0,
  totalBytes: 0,
  downloadUrl: null,
  tempFilePath: null,
  lastProgressTime: Date.now()
};

/**
 * 配置 electron-updater 的高级选项
 */
function configureUpdaterAdvanced() {
  // 设置更长的超时时间
  if (autoUpdater.httpExecutor) {
    // 设置HTTP请求超时为10分钟
    autoUpdater.httpExecutor.maxRedirects = 10;

    // 如果有requestOptions，设置超时
    if (autoUpdater.httpExecutor.requestOptions) {
      autoUpdater.httpExecutor.requestOptions.timeout = 600000; // 10分钟
    }
  }

  // 设置请求头，避免缓存问题
  autoUpdater.requestHeaders = {
    'Cache-Control': 'no-cache',
    'User-Agent': 'AiTools-Updater/1.0'
  };

  // 禁用自动安装，让用户控制
  autoUpdater.autoInstallOnAppQuit = false;

  console.log('electron-updater 高级配置已应用');
}

/**
 * 支持断点续传的下载函数
 * @param {string} url - 下载URL
 * @param {string} filePath - 保存文件路径
 * @param {Function} onProgress - 进度回调
 * @returns {Promise} 下载Promise
 */
function downloadWithResume(url, filePath, onProgress) {
  return new Promise((resolve, reject) => {
    let startByte = 0;

    // 检查是否存在部分下载的文件
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      startByte = stats.size;
      console.log(`发现部分下载文件，从字节 ${startByte} 继续下载`);
    }

    const protocol = url.startsWith('https:') ? https : http;
    const options = {
      headers: {
        'Range': `bytes=${startByte}-`,
        'User-Agent': 'AiTools-Updater/1.0'
      }
    };

    const request = protocol.get(url, options, (response) => {
      const statusCode = response.statusCode;

      if (statusCode === 206 || (statusCode === 200 && startByte === 0)) {
        // 206: 部分内容（续传）, 200: 完整内容（新下载）
        const totalSize = parseInt(response.headers['content-length']) + startByte;
        const writeStream = fs.createWriteStream(filePath, { flags: startByte > 0 ? 'a' : 'w' });

        let downloadedBytes = startByte;

        response.on('data', (chunk) => {
          downloadedBytes += chunk.length;
          writeStream.write(chunk);

          if (onProgress) {
            onProgress({
              transferred: downloadedBytes,
              total: totalSize,
              percent: (downloadedBytes / totalSize) * 100,
              bytesPerSecond: 0, // 可以计算，但这里简化
              delta: chunk.length
            });
          }
        });

        response.on('end', () => {
          writeStream.end();
          console.log('下载完成');
          resolve({ path: filePath, size: downloadedBytes });
        });

        response.on('error', (error) => {
          writeStream.destroy();
          reject(error);
        });

        writeStream.on('error', (error) => {
          reject(error);
        });

      } else if (statusCode === 416) {
        // 416: 请求范围不满足，文件可能已经完整
        console.log('文件可能已经下载完成');
        resolve({ path: filePath, size: startByte });
      } else {
        reject(new Error(`HTTP ${statusCode}: ${response.statusMessage}`));
      }
    });

    request.on('error', (error) => {
      reject(error);
    });

    // 设置超时
    request.setTimeout(600000, () => { // 10分钟超时
      request.destroy();
      reject(new Error('下载超时'));
    });
  });
}

/**
 * 创建带有超时控制的下载Promise
 * @param {number} timeoutMs - 超时时间（毫秒）
 * @returns {Promise} 下载Promise
 */
function createDownloadWithTimeout(timeoutMs = 600000) { // 默认10分钟
  return new Promise((resolve, reject) => {
    let isResolved = false;
    let progressTimeout;

    // 设置总超时
    const totalTimeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        reject(new Error(`下载总超时 (${timeoutMs / 1000}秒)`));
      }
    }, timeoutMs);

    // 设置进度超时（如果60秒内没有进度更新，认为连接断开）
    const resetProgressTimeout = () => {
      if (progressTimeout) clearTimeout(progressTimeout);
      progressTimeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          clearTimeout(totalTimeoutId);
          reject(new Error('下载进度超时，连接可能已断开'));
        }
      }, 60000); // 60秒无进度则超时
    };

    resetProgressTimeout();

    // 监听下载进度
    const onProgress = (progress) => {
      resetProgressTimeout(); // 有进度就重置超时
      downloadState.downloadedBytes = progress.transferred;
      downloadState.totalBytes = progress.total;
      downloadState.lastProgressTime = Date.now();
    };

    // 监听下载完成
    const onDownloaded = (info) => {
      if (!isResolved) {
        isResolved = true;
        clearTimeout(totalTimeoutId);
        if (progressTimeout) clearTimeout(progressTimeout);
        autoUpdater.removeListener('update-downloaded', onDownloaded);
        autoUpdater.removeListener('error', onError);
        autoUpdater.removeListener('download-progress', onProgress);
        resolve(info);
      }
    };

    // 监听错误
    const onError = (error) => {
      if (!isResolved) {
        isResolved = true;
        clearTimeout(totalTimeoutId);
        if (progressTimeout) clearTimeout(progressTimeout);
        autoUpdater.removeListener('update-downloaded', onDownloaded);
        autoUpdater.removeListener('error', onError);
        autoUpdater.removeListener('download-progress', onProgress);
        reject(error);
      }
    };

    autoUpdater.on('update-downloaded', onDownloaded);
    autoUpdater.on('error', onError);
    autoUpdater.on('download-progress', onProgress);

    // 开始下载
    autoUpdater.downloadUpdate().catch(onError);
  });
}

/**
 * 带重试的下载函数，支持智能重试和进度监控
 * @param {number} maxRetries - 最大重试次数
 * @param {number} timeoutMs - 每次尝试的超时时间
 * @returns {Promise} 下载结果
 */
async function downloadWithRetry(maxRetries = 5, timeoutMs = 600000) {
  let lastError;
  let consecutiveFailures = 0;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`开始第 ${attempt} 次下载尝试...`);

      // 每次重试前重新配置
      configureUpdaterAdvanced();

      // 重置下载状态
      downloadState.isDownloading = true;
      downloadState.downloadedBytes = 0;
      downloadState.totalBytes = 0;
      downloadState.lastProgressTime = Date.now();

      const result = await createDownloadWithTimeout(timeoutMs);
      console.log(`第 ${attempt} 次下载尝试成功`);

      downloadState.isDownloading = false;
      consecutiveFailures = 0; // 重置连续失败计数
      return result;

    } catch (error) {
      lastError = error;
      consecutiveFailures++;
      downloadState.isDownloading = false;

      console.error(`第 ${attempt} 次下载尝试失败:`, error.message);

      // 分析错误类型，决定重试策略
      const isNetworkError = isRetryableError(error);
      const isProgressTimeout = error.message.includes('进度超时');

      if (attempt < maxRetries && (isNetworkError || isProgressTimeout)) {
        // 根据错误类型调整延迟时间
        let delay;
        if (isProgressTimeout) {
          // 进度超时，可能是网络慢，延迟短一些
          delay = Math.min(2000 * attempt, 10000); // 2秒到10秒
        } else {
          // 其他网络错误，使用指数退避
          delay = Math.min(3000 * Math.pow(2, attempt - 1), 30000);
        }

        console.log(`检测到${isProgressTimeout ? '进度超时' : '网络错误'}，等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));

        // 如果连续失败次数过多，增加额外延迟
        if (consecutiveFailures >= 3) {
          console.log('连续失败次数较多，增加额外延迟...');
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      } else if (attempt >= maxRetries) {
        console.log('已达到最大重试次数');
        break;
      } else {
        console.log('非网络错误，停止重试');
        break;
      }
    }
  }

  throw lastError;
}

/**
 * 检查错误是否可重试
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否可重试
 */
function isRetryableError(error) {
  if (!error) return false;
  
  const message = error.message || String(error);
  const retryablePatterns = [
    'timeout',
    'TIMEOUT',
    'ECONNRESET',
    'ENOTFOUND',
    'ETIMEDOUT',
    'socket hang up',
    'ERR_CONNECTION_RESET',
    'ERR_CONNECTION_CLOSED',
    'Request timeout',
    'Download timeout',
    'ERR_SOCKET_TIMEOUT',
    'ERR_STREAM_PREMATURE_CLOSE'
  ];
  
  return retryablePatterns.some(pattern => 
    message.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * 获取当前下载状态
 * @returns {object} 下载状态信息
 */
function getDownloadState() {
  return {
    ...downloadState,
    isStalled: downloadState.isDownloading &&
               (Date.now() - downloadState.lastProgressTime) > 30000 // 30秒无进度认为卡住
  };
}

/**
 * 重置下载状态
 */
function resetDownloadState() {
  downloadState.isDownloading = false;
  downloadState.downloadedBytes = 0;
  downloadState.totalBytes = 0;
  downloadState.downloadUrl = null;
  downloadState.tempFilePath = null;
  downloadState.lastProgressTime = Date.now();
}

/**
 * 强制清理下载资源
 */
function cleanupDownload() {
  try {
    if (downloadState.tempFilePath && fs.existsSync(downloadState.tempFilePath)) {
      fs.unlinkSync(downloadState.tempFilePath);
      console.log('已清理临时下载文件');
    }
  } catch (error) {
    console.warn('清理下载文件失败:', error.message);
  }
  resetDownloadState();
}

module.exports = {
  configureUpdaterAdvanced,
  createDownloadWithTimeout,
  downloadWithRetry,
  downloadWithResume,
  isRetryableError,
  getDownloadState,
  resetDownloadState,
  cleanupDownload
};
